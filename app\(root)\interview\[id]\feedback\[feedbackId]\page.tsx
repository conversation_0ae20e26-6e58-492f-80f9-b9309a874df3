import { getCurrentUser } from '@/lib/actions/auth.action';
import { getInterviewsById, getFeedbackById } from '@/lib/actions/general.action';
import { redirect } from 'next/navigation';
import React from 'react'

const page = async({params}:RouteParams) => {
  const{ id, feedbackId } = await params; // id = interview ID, feedbackId = feedback ID
  const user = await getCurrentUser();
  const interview = await getInterviewsById(id);
  if (!interview) redirect('/');

  // Get specific feedback by ID
  const feedback = await getFeedbackById(feedbackId);

  // Check if feedback exists
  if (!feedback) {
    return (
      <div className="text-center p-8">
        <h2>Feedback Not Found</h2>
        <p>The requested feedback could not be found.</p>
        <a href={`/interview/${id}`} className="text-blue-500 underline">
          Go back to interview
        </a>
      </div>
    );
  }
  

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">Interview Feedback</h1>
      <div className="bg-gray-100 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Interview: {interview.role}</h2>
        <p><strong>Total Score:</strong> {feedback.totalScore}/100</p>
        <div className="mt-4">
          <h3 className="font-semibold">Strengths:</h3>
          <ul className="list-disc list-inside">
            {feedback.strengths?.map((strength: string, index: number) => (
              <li key={index}>{strength}</li>
            ))}
          </ul>
        </div>
        <div className="mt-4">
          <h3 className="font-semibold">Areas for Improvement:</h3>
          <ul className="list-disc list-inside">
            {feedback.areasForImprovement?.map((area: string, index: number) => (
              <li key={index}>{area}</li>
            ))}
          </ul>
        </div>
        <div className="mt-4">
          <h3 className="font-semibold">Final Assessment:</h3>
          <p>{feedback.finalAssessment}</p>
        </div>
      </div>
    </div>
  )
}

export default page
