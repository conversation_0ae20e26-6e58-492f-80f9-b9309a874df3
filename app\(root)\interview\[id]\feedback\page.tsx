import { getCurrentUser } from '@/lib/actions/auth.action';
import { getFeedbackByInterviewId, getInterviewsById } from '@/lib/actions/general.action';
import { redirect } from 'next/navigation';
import React from 'react'

const page = async({params}:RouteParams) => {
  const{ id } = await params; // This is the interview ID
  const user = await getCurrentUser();
  const interview = await getInterviewsById(id);
  if (!interview) redirect('/');

  // Get feedback by interview ID - this should return the feedback, not the interview
  console.log('🔍 Looking for feedback with:', {
    interviewId: id,
    userId: user?.uid,
  });

  const feedback = await getFeedbackByInterviewId({
    interviewId: id,
    userId: user?.uid || '',
  });

  console.log('📊 Interview data:', interview);
  console.log('💬 Feedback data:', feedback);
  console.log('📝 Feedback type:', typeof feedback);

  // Check if feedback exists
  if (!feedback) {
    console.log('❌ No feedback found for this interview');
    return (
      <div className="text-center p-8">
        <h2>No Feedback Available</h2>
        <p>Feedback hasn&apos;t been generated for this interview yet.</p>
        <p>Complete the interview first to generate feedback.</p>
      </div>
    );
  }
  

  return (
    <div>
      
    </div>
  )
}

export default page
