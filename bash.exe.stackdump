Stack trace:
Frame         Function      Args
0007FFFFBE20  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFFAD20) msys-2.0.dll+0x2118E
0007FFFFBE20  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x69BA
0007FFFFBE20  0002100469F2 (00021028DF99, 0007FFFFBCD8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBE20  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBE20  00021006A545 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFC100  00021006B9A5 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC5A900000 ntdll.dll
7FFC58F60000 KERNEL32.DLL
7FFC57F90000 KERNELBASE.dll
7FFC5A580000 USER32.dll
7FFC57F60000 win32u.dll
7FFC59C10000 GDI32.dll
7FFC58560000 gdi32full.dll
7FFC584B0000 msvcp_win.dll
7FFC57C90000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC593C0000 advapi32.dll
7FFC58EB0000 msvcrt.dll
7FFC586A0000 sechost.dll
7FFC58C20000 RPCRT4.dll
7FFC57170000 CRYPTBASE.DLL
7FFC58410000 bcryptPrimitives.dll
7FFC58D40000 IMM32.DLL
